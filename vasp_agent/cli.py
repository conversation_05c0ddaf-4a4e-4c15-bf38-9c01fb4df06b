"""
Command Line Interface for VASP Agent.

This module provides the main CLI commands for planning and running
VASP calculations from natural language descriptions.
"""

import sys
import json
import click
from pathlib import Path
from typing import Optional

from .tools.parser import parse_prompt_to_taskspec, parse_material_only
from .tools.fs import new_run_dir
from .tools.runner import LocalDryRunner
from .tools.reporting import write_report, format_report_summary
from .workflows.planner import plan_from_taskspec, print_workflow_plan


@click.group()
@click.version_option(version="0.1.0")
def main():
    """
    VASP Agent - AI-powered VASP simulation workflow management.
    
    Convert natural language descriptions into VASP calculation workflows.
    """
    pass


@main.command()
@click.option(
    "--material", 
    required=True, 
    help="Material identifier (formula, mp-id, filename)"
)
@click.option(
    "--source", 
    required=True, 
    type=click.Choice(["formula", "mp-id", "cif", "poscar"]),
    help="Source type of the material specification"
)
@click.option(
    "--output", 
    default=None, 
    help="Output file for TaskSpec JSON (default: stdout)"
)
def plan(material: str, source: str, output: Optional[str]):
    """
    Plan a calculation workflow for a given material.
    
    This command creates a TaskSpec from material specification and prints
    it as JSON. Useful for understanding what workflow would be generated.
    """
    try:
        # Parse material specification
        material_spec = parse_material_only(material, source)
        
        # Create a basic TaskSpec with standard operations
        from .schemas import TaskSpec, OperationSpec, TheorySpec, NumericsSpec
        
        task_spec = TaskSpec(
            material=material_spec,
            operations=[
                OperationSpec(type="pre_relax"),
                OperationSpec(type="relax"),
                OperationSpec(type="static"),
            ],
            theory=TheorySpec(),
            numerics=NumericsSpec()
        )
        
        # Convert to JSON
        task_spec_dict = {
            "material": {
                "value": task_spec.material.value,
                "source": task_spec.material.source
            },
            "operations": [
                {"type": op.type, "parameters": op.parameters} 
                for op in task_spec.operations
            ],
            "theory": {
                "xc": task_spec.theory.xc,
                "u_settings": task_spec.theory.u_settings
            },
            "numerics": {
                "preset": task_spec.numerics.preset,
                "custom": task_spec.numerics.custom
            }
        }
        
        # Output JSON
        json_output = json.dumps(task_spec_dict, indent=2)
        
        if output:
            Path(output).write_text(json_output)
            click.echo(f"TaskSpec written to {output}")
        else:
            click.echo(json_output)
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@main.command()
@click.option(
    "--prompt", 
    default=None, 
    help="Natural language description of the calculation"
)
@click.option(
    "--stdin", 
    is_flag=True, 
    help="Read prompt from stdin instead of --prompt"
)
@click.option(
    "--output-dir", 
    default="./runs", 
    help="Base directory for run outputs"
)
@click.option(
    "--dry-run", 
    is_flag=True, 
    default=True, 
    help="Perform dry run simulation (default: True)"
)
@click.option(
    "--verbose", 
    is_flag=True, 
    help="Enable verbose output"
)
def run(prompt: Optional[str], stdin: bool, output_dir: str, dry_run: bool, verbose: bool):
    """
    Run a complete VASP calculation workflow from natural language description.
    
    This command parses the prompt, plans the workflow, executes it (dry run),
    and generates a comprehensive report.
    """
    try:
        # Get prompt text
        if stdin:
            if verbose:
                click.echo("Reading prompt from stdin...")
            prompt_text = sys.stdin.read().strip()
        elif prompt:
            prompt_text = prompt
        else:
            click.echo("Error: Must provide either --prompt or --stdin", err=True)
            sys.exit(1)
        
        if not prompt_text:
            click.echo("Error: Empty prompt provided", err=True)
            sys.exit(1)
        
        if verbose:
            click.echo(f"Processing prompt: {prompt_text}")
        
        # Parse prompt to TaskSpec
        task_spec = parse_prompt_to_taskspec(prompt_text)
        
        if verbose:
            click.echo(f"Detected material: {task_spec.material.value} ({task_spec.material.source})")
            click.echo(f"Operations: {[op.type for op in task_spec.operations]}")
            click.echo(f"Theory: {task_spec.theory.xc}")
        
        # Plan workflow
        dag = plan_from_taskspec(task_spec)
        
        if verbose:
            click.echo("\nWorkflow Plan:")
            print_workflow_plan(dag)
            click.echo("")
        
        # Create run directory
        run_dir = new_run_dir(base=output_dir, material_hint=task_spec.material.value)
        click.echo(f"Created run directory: {run_dir}")
        
        # Execute workflow
        if dry_run:
            runner = LocalDryRunner(base_dir=output_dir)
            
            # Execute each operation node
            linearized_nodes = dag.linearized()
            operation_nodes = [node for node in linearized_nodes if node.name != "summarize"]
            
            for node in operation_nodes:
                if verbose:
                    click.echo(f"Executing {node.name}...")
                
                # Prepare step
                prep_result = runner.prepare_step(run_dir, node.name, task_spec)
                
                # Execute step
                exec_result = runner.execute_step(run_dir, node.name)
                
                if verbose:
                    click.echo(f"  Status: {exec_result['status']}")
                    if node.name == "static":
                        click.echo(f"  Final energy: {exec_result['final_energy']:.6f} eV")
            
            # Summarize results
            if verbose:
                click.echo("Generating summary...")
            
            summary = runner.summarize(run_dir)
            
            # Write report
            report_path = write_report(run_dir, summary, task_spec)
            
            click.echo(f"Calculation completed successfully!")
            click.echo(f"Report written to: {report_path}")
            
            # Display summary
            if verbose:
                click.echo("\nResults Summary:")
                click.echo(f"  Final Energy: {summary['final_energy']:.6f} eV")
                click.echo(f"  Band Gap: {summary['band_gap']:.3f} eV")
                click.echo(f"  Material Type: {'Metal' if summary['is_metal'] else 'Insulator'}")
                click.echo(f"  Formula: {summary['formula']}")
        
        else:
            click.echo("Error: Real VASP execution not implemented in this version", err=True)
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


@main.command()
@click.argument("run_dir", type=click.Path(exists=True))
@click.option(
    "--format", 
    "output_format",
    type=click.Choice(["json", "summary"]),
    default="summary",
    help="Output format for the report"
)
def report(run_dir: str, output_format: str):
    """
    Display or export a calculation report.
    
    RUN_DIR is the path to a completed calculation run directory.
    """
    try:
        report_path = Path(run_dir) / "report.json"
        
        if not report_path.exists():
            click.echo(f"Error: No report found in {run_dir}", err=True)
            sys.exit(1)
        
        from .tools.reporting import read_report
        report_data = read_report(str(report_path))
        
        if output_format == "json":
            click.echo(json.dumps(report_data, indent=2))
        else:
            formatted_report = format_report_summary(report_data)
            click.echo(formatted_report)
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@main.command()
@click.option(
    "--base-dir", 
    default="./runs", 
    help="Base directory containing run directories"
)
@click.option(
    "--output", 
    default=None, 
    help="Output file for summary (default: stdout)"
)
def summary(base_dir: str, output: Optional[str]):
    """
    Generate a summary report from multiple calculation runs.
    """
    try:
        from .tools.fs import list_run_dirs
        from .tools.reporting import generate_summary_report
        
        # Get all run directories
        run_names = list_run_dirs(base_dir)
        run_dirs = [str(Path(base_dir) / name) for name in run_names]
        
        if not run_dirs:
            click.echo(f"No run directories found in {base_dir}")
            return
        
        # Generate summary
        summary_data = generate_summary_report(run_dirs)
        
        # Format output
        summary_json = json.dumps(summary_data, indent=2)
        
        if output:
            Path(output).write_text(summary_json)
            click.echo(f"Summary written to {output}")
        else:
            click.echo(summary_json)
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
