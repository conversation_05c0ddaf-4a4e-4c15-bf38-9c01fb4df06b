"""
Core Pydantic schemas for VASP Agent.

This module defines all the data structures used throughout the VASP Agent system,
including TaskSpec, WorkflowDAG, and various component specifications.
"""

from typing import Dict, List, Any, Optional, Literal, Callable
from pydantic import BaseModel, Field, field_validator, ConfigDict
from abc import ABC, abstractmethod


class MaterialSpec(BaseModel):
    """Specification for a material to be calculated."""
    value: str = Field(..., description="Material identifier (formula, mp-id, filename)")
    source: Literal["formula", "mp-id", "cif", "poscar"] = Field(
        ..., description="Source type of the material specification"
    )

    model_config = ConfigDict(frozen=True)


class OperationSpec(BaseModel):
    """Specification for a single calculation operation."""
    type: Literal["pre_relax", "relax", "static", "bands", "dos"] = Field(
        ..., description="Type of calculation operation"
    )
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Operation-specific parameters"
    )

    model_config = ConfigDict(frozen=True)


class TheorySpec(BaseModel):
    """Specification for DFT theory settings."""
    xc: Literal["PBE", "HSE06"] = Field(default="PBE", description="Exchange-correlation functional")
    u_settings: Dict[str, float] = Field(
        default_factory=dict, description="Hubbard U settings for elements"
    )

    model_config = ConfigDict(frozen=True)


class NumericsSpec(BaseModel):
    """Specification for numerical settings."""
    preset: Literal["standard", "fast", "journal"] = Field(
        default="standard", description="Preset for numerical accuracy"
    )
    custom: Dict[str, Any] = Field(
        default_factory=dict, description="Custom numerical parameters"
    )

    model_config = ConfigDict(frozen=True)


class TaskSpec(BaseModel):
    """Complete specification for a VASP calculation task."""
    material: MaterialSpec = Field(..., description="Material to calculate")
    operations: List[OperationSpec] = Field(..., description="List of operations to perform")
    theory: TheorySpec = Field(default_factory=TheorySpec, description="DFT theory settings")
    numerics: NumericsSpec = Field(default_factory=NumericsSpec, description="Numerical settings")

    @field_validator('operations')
    @classmethod
    def validate_operations(cls, v):
        """Ensure operations list is not empty and contains valid sequence."""
        if not v:
            raise ValueError("Operations list cannot be empty")
        
        # Check for required base operations
        op_types = [op.type for op in v]
        required_base = ["pre_relax", "relax", "static"]
        
        for req_op in required_base:
            if req_op not in op_types:
                raise ValueError(f"Required operation '{req_op}' missing from operations")
        
        return v

    model_config = ConfigDict(frozen=True)


class Node(BaseModel, ABC):
    """Abstract base class for workflow nodes."""
    name: str = Field(..., description="Unique name for this node")
    dependencies: List[str] = Field(default_factory=list, description="Names of prerequisite nodes")

    @abstractmethod
    def run(self, ctx: Dict[str, Any]) -> Dict[str, Any]:
        """Execute this node with the given context."""
        pass

    model_config = ConfigDict(arbitrary_types_allowed=True)


class WorkflowDAG(BaseModel):
    """Directed Acyclic Graph representing a workflow."""
    nodes: Dict[str, Node] = Field(default_factory=dict, description="Nodes in the workflow")
    
    def add_node(self, node: Node) -> None:
        """Add a node to the workflow."""
        self.nodes[node.name] = node
    
    def linearized(self) -> List[Node]:
        """Return nodes in topological order for execution."""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(node_name: str):
            if node_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving node '{node_name}'")
            if node_name in visited:
                return
            
            temp_visited.add(node_name)
            node = self.nodes[node_name]
            
            for dep in node.dependencies:
                if dep not in self.nodes:
                    raise ValueError(f"Dependency '{dep}' not found for node '{node_name}'")
                visit(dep)
            
            temp_visited.remove(node_name)
            visited.add(node_name)
            result.append(node)
        
        for node_name in self.nodes:
            if node_name not in visited:
                visit(node_name)
        
        return result

    model_config = ConfigDict(arbitrary_types_allowed=True)


class RunContext(BaseModel):
    """Context object passed between workflow nodes."""
    workdir: str = Field(..., description="Working directory for this run")
    plan_log: List[str] = Field(default_factory=list, description="Log of executed steps")
    summary: Dict[str, Any] = Field(default_factory=dict, description="Summary data for reporting")
    ops: List[str] = Field(default_factory=list, description="Ordered list of operation names")
    current_op: Optional[str] = Field(default=None, description="Currently executing operation")

    model_config = ConfigDict(arbitrary_types_allowed=True)


class ReportData(BaseModel):
    """Structure for final calculation report."""
    final_energy: float = Field(..., description="Final total energy in eV")
    band_gap: float = Field(..., description="Band gap in eV")
    is_metal: bool = Field(..., description="Whether material is metallic")
    formula: str = Field(..., description="Chemical formula")
    run_id: str = Field(..., description="Unique run identifier")
    timestamp: str = Field(..., description="Calculation timestamp")
    operations_completed: List[str] = Field(..., description="List of completed operations")

    model_config = ConfigDict(frozen=True)
