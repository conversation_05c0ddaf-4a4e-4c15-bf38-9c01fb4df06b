"""
Comprehensive tests for VASP Agent parser, planner, and runner components.

This module contains the test cases specified in the project requirements
to ensure all components work correctly together.
"""

import pytest
import tempfile
import json
from pathlib import Path

from vasp_agent.tools.parser import parse_prompt_to_taskspec
from vasp_agent.workflows.planner import plan_from_taskspec
from vasp_agent.tools.runner import LocalDryRunner
from vasp_agent.tools.fs import new_run_dir
from vasp_agent.tools.reporting import write_report


def test_parse_ops_and_u():
    """Test parsing operations and U settings from prompt."""
    ts = parse_prompt_to_taskspec("Band structure and DOS of LiFePO4 with U(Fe)=5.3")
    
    # Check that bands and dos operations are included
    op_types = [op.type for op in ts.operations]
    assert "bands" in op_types, "bands operation should be detected"
    assert "dos" in op_types, "dos operation should be detected"
    
    # Check U settings
    assert "fe" in ts.theory.u_settings, "U setting for Fe should be detected"
    assert ts.theory.u_settings["fe"] == 5.3, "U(Fe) value should be 5.3"


def test_parse_basic_prompt():
    """Test parsing a basic prompt."""
    ts = parse_prompt_to_taskspec("Compute PBE band gap of LiFePO4")
    
    # Check material detection
    assert ts.material.value == "LiFePO4"
    assert ts.material.source == "formula"
    
    # Check operations (should include bands due to "band gap")
    op_types = [op.type for op in ts.operations]
    assert "pre_relax" in op_types
    assert "relax" in op_types
    assert "static" in op_types
    assert "bands" in op_types
    
    # Check theory settings
    assert ts.theory.xc == "PBE"


def test_parse_hse_prompt():
    """Test parsing HSE functional."""
    ts = parse_prompt_to_taskspec("HSE06 calculation of CaTiO3")
    
    assert ts.material.value == "CaTiO3"
    assert ts.theory.xc == "HSE06"


def test_parse_mp_id():
    """Test parsing Materials Project ID."""
    ts = parse_prompt_to_taskspec("Calculate mp-1234 electronic structure")
    
    assert ts.material.value == "mp-1234"
    assert ts.material.source == "mp-id"


def test_parse_fast_preset():
    """Test parsing fast preset."""
    ts = parse_prompt_to_taskspec("Fast calculation of LiFePO4")
    
    assert ts.numerics.preset == "fast"


def test_plan_order():
    """Test that planner creates correct operation order."""
    ts = parse_prompt_to_taskspec("Compute PBE gap of LiFePO4")
    dag = plan_from_taskspec(ts)
    
    # Get linearized execution order
    linearized = dag.linearized()
    names = [n.name for n in linearized]
    
    # Check that basic sequence is correct
    assert names[:3] == ["pre_relax", "relax", "static"], "Basic sequence should be pre_relax → relax → static"
    assert "summarize" in names, "summarize node should be present"
    assert names[-1] == "summarize", "summarize should be the last node"


def test_plan_with_bands_and_dos():
    """Test planning with bands and DOS operations."""
    ts = parse_prompt_to_taskspec("Band structure and DOS of LiFePO4")
    dag = plan_from_taskspec(ts)
    
    linearized = dag.linearized()
    names = [n.name for n in linearized]
    
    assert "bands" in names
    assert "dos" in names
    assert "summarize" in names
    assert names[-1] == "summarize"


def test_run_smoke(tmp_path):
    """Smoke test for complete workflow execution."""
    ts = parse_prompt_to_taskspec("Compute PBE band gap of LiFePO4")
    run_dir = new_run_dir(base=str(tmp_path), material_hint="LiFePO4")
    
    # Create workflow
    dag = plan_from_taskspec(ts)
    
    # Initialize context
    ctx = {"workdir": str(run_dir)}
    
    # Simulate node execution using runner
    runner = LocalDryRunner(base_dir=str(tmp_path))
    
    # Execute operation nodes (excluding summarize)
    linearized = dag.linearized()
    operation_nodes = [node for node in linearized if node.name != "summarize"]
    
    for node in operation_nodes:
        # Prepare and execute step
        runner.prepare_step(ctx["workdir"], node.name, ts)
        runner.execute_step(ctx["workdir"], node.name)
    
    # Generate summary
    ctx["summary"] = runner.summarize(ctx["workdir"])
    
    # Write report
    report_path = write_report(str(run_dir), ctx["summary"], ts)
    
    # Verify report exists and has correct structure
    assert Path(report_path).exists(), "Report file should be created"
    
    with open(report_path, 'r') as f:
        data = json.load(f)
    
    # Check required fields
    required_fields = ["band_gap", "final_energy", "is_metal", "formula"]
    for field in required_fields:
        assert field in data, f"Report should contain {field}"
    
    # Check data types and ranges
    assert isinstance(data["band_gap"], (int, float)), "band_gap should be numeric"
    assert isinstance(data["final_energy"], (int, float)), "final_energy should be numeric"
    assert isinstance(data["is_metal"], bool), "is_metal should be boolean"
    assert isinstance(data["formula"], str), "formula should be string"
    assert data["band_gap"] >= 0, "band_gap should be non-negative"


def test_runner_prepare_step(tmp_path):
    """Test runner prepare_step functionality."""
    ts = parse_prompt_to_taskspec("Static calculation of LiFePO4")
    run_dir = new_run_dir(base=str(tmp_path), material_hint="LiFePO4")
    
    runner = LocalDryRunner(base_dir=str(tmp_path))
    result = runner.prepare_step(run_dir, "static", ts)
    
    # Check result structure
    assert result["operation"] == "static"
    assert "directory" in result
    assert "files_created" in result
    
    # Check that files were created
    op_dir = Path(result["directory"])
    assert op_dir.exists()
    assert (op_dir / "INCAR").exists()
    assert (op_dir / "KPOINTS").exists()
    assert (op_dir / "POSCAR").exists()


def test_runner_execute_step(tmp_path):
    """Test runner execute_step functionality."""
    ts = parse_prompt_to_taskspec("Static calculation of LiFePO4")
    run_dir = new_run_dir(base=str(tmp_path), material_hint="LiFePO4")
    
    runner = LocalDryRunner(base_dir=str(tmp_path))
    
    # Prepare first
    runner.prepare_step(run_dir, "static", ts)
    
    # Then execute
    result = runner.execute_step(run_dir, "static")
    
    # Check result structure
    assert result["operation"] == "static"
    assert result["status"] == "completed"
    assert "final_energy" in result
    assert isinstance(result["final_energy"], (int, float))


def test_runner_summarize(tmp_path):
    """Test runner summarize functionality."""
    ts = parse_prompt_to_taskspec("Band structure of LiFePO4")
    run_dir = new_run_dir(base=str(tmp_path), material_hint="LiFePO4")
    
    runner = LocalDryRunner(base_dir=str(tmp_path))
    
    # Execute static and bands operations
    for op in ["static", "bands"]:
        runner.prepare_step(run_dir, op, ts)
        runner.execute_step(run_dir, op)
    
    # Summarize
    summary = runner.summarize(run_dir)
    
    # Check summary structure
    required_fields = ["final_energy", "band_gap", "is_metal", "formula"]
    for field in required_fields:
        assert field in summary, f"Summary should contain {field}"
    
    assert isinstance(summary["band_gap"], (int, float))
    assert summary["band_gap"] >= 0


def test_workflow_validation():
    """Test workflow validation."""
    ts = parse_prompt_to_taskspec("Compute PBE band gap of LiFePO4")
    dag = plan_from_taskspec(ts)
    
    # Should not raise any exceptions
    from vasp_agent.workflows.planner import validate_workflow
    validate_workflow(dag, ts)


def test_error_handling():
    """Test error handling for invalid inputs."""
    # Test invalid material
    with pytest.raises(ValueError):
        parse_prompt_to_taskspec("Calculate something with no material")
    
    # Test empty prompt
    with pytest.raises(ValueError):
        parse_prompt_to_taskspec("")


def test_policy_integration():
    """Test integration with policy system."""
    from vasp_agent.tools.policy import encut_from_potcars, choose_smearing
    
    # Test ENCUT calculation as specified in requirements
    encut = encut_from_potcars([400, 520], "1.3*ENMAX")
    assert encut == 676, "ENCUT should be 1.3 * max(ENMAX) = 1.3 * 520 = 676"
    
    # Test smearing selection
    metal_smearing = choose_smearing(is_metal_hint=True, preset="standard")
    assert "ISMEAR" in metal_smearing
    assert "SIGMA" in metal_smearing
    
    insulator_smearing = choose_smearing(is_metal_hint=False, preset="standard")
    assert "ISMEAR" in insulator_smearing
    assert "SIGMA" in insulator_smearing


if __name__ == "__main__":
    pytest.main([__file__])
