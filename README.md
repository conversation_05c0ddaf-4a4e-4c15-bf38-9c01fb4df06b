# VASP Agent

AI-powered VASP simulation workflow management system that converts natural language requests into planned DFT workflows.

## Features

- **Natural Language Interface**: Convert plain English descriptions into VASP workflows
- **Automated Workflow Planning**: Generate optimized calculation sequences (pre-relax → relax → static → bands/DOS)
- **Policy-Driven Input Generation**: Intelligent INCAR/KPOINTS generation based on best practices
- **Dry-Run Simulation**: Test workflows without actual VASP execution
- **Comprehensive Reporting**: JSON reports with energy, band gap, and material properties

## Installation

```bash
pip install -e .
```

## Quick Start

### Plan a calculation
```bash
vasp-agent plan --material LiFePO4 --source formula
```

### Run a workflow from natural language
```bash
echo "Compute PBE band gap of LiFePO4" | vasp-agent run --stdin
```

### Run with direct prompt
```bash
vasp-agent run --prompt "Band structure and DOS of LiFePO4 with U(Fe)=5.3"
```

## Requirements

- Python 3.10+
- pydantic v2
- pymatgen
- click

## Development

```bash
pip install -e ".[dev]"
pytest
```

## License

MIT License
