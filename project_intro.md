# 0) Coordinator / Project Brief (give this to the “Lead” agent)

**Objective**
Assemble a minimal, working VASP agent that turns a natural-language request into a planned workflow (pre-relax → relax → static → bands), generates inputs from policy, sets up a run directory, and (for now) performs a dry-run with artifacts + a JSON report.

**Scope (must-have)**

* Interaction layer (CLI) with two commands: `plan` and `run`.
* Semantic parser: `parse_prompt_to_taskspec(prompt: str) -> TaskSpec`.
* Domain knowledge base (YAML policies + helpers).
* Planner: build a `WorkflowDAG` from `TaskSpec`.
* Runner: Local dry-run runner that writes files, simulates outputs.
* File/resource management: per-run directories, POTCAR map checks, provenance JSON.

**Out of scope (this iteration)**
Real VASP execution, SLURM integration, phonons/NEB/defects.

**Acceptance**

* `pip install -e .` works.
* `vasp-agent plan --material LiFePO4 --source formula` prints a valid JSON TaskSpec.
* `echo "Compute PBE band gap of LiFePO4" | vasp-agent run --stdin` creates `./runs/demo/report.json` with keys `final_energy`, `band_gap`, `is_metal`, `formula`.
* `pytest -q` passes provided tests.

**Conventions**

* Python 3.10+, `pydantic` v2 models.
* Keep pure functions where possible; no hidden globals.
* All I/O paths under `./runs/<run_id>`.

Remember, all the code names in this file is just an example name, you may not find them in the repo and you need to create it.
also the code content is just example, you need to write your own code.
---

# 1) Interaction Layer (CLI) Prompt

**Role**: Implement a friendly CLI that accepts either flags or stdin text and wires the pipeline.

**Task**

* Extend `vasp_agent/cli.py` with:

  * `plan` command: takes `--material`, `--source` and prints `TaskSpec`.
  * `run` command: reads prompt from `--prompt` or stdin; uses parser → planner → runner → post-proc; prints path to report.

**Details**

* If `--stdin` is set, read entire stdin as the user prompt.
* Store outputs under `./runs/<slugified-material>-<YYYYmmdd-HHMMSS>/`.
* Log a short “plan” preview before execution: nodes in order.

**Definition of Done**

* `vasp-agent run --prompt "Compute PBE band gap of LiFePO4"` produces a run dir and a JSON report.
* CLI returns non-zero exit on exceptions and prints a helpful message.

---

# 2) Semantic Parser Prompt

**Role**: Turn messy user text into a **validated** `TaskSpec`.

**Task**
Create `vasp_agent/tools/parser.py` with function:

```python
def parse_prompt_to_taskspec(prompt: str) -> TaskSpec:
    ...
```

**Rules (simple, deterministic)**

* Detect **material** by:

  * explicit `mp-` ID → `source="mp-id"`;
  * CIF/POSCAR filenames in text → `source="cif"|"poscar"`;
  * else first valid chemical formula token → `source="formula"`.
* Infer **operations**:

  * Always include: `pre_relax → relax → static`.
  * If text mentions “band”, “band structure”, “gap” → append `bands`.
  * If text mentions “dos” → append `dos`.
* Theory:

  * Default `xc="PBE"`.
  * If text has “HSE” or “HSE06” → `xc="HSE06"`.
  * Simple +U hints: patterns like `U(Fe)=5.3` → `u_settings={"Fe":5.3}`.
* Numerics:

  * `preset="standard"` unless the text includes “fast” or “journal”.

**Validation**

* Return a fully populated `TaskSpec` (no empty fields).
* Raise `ValueError` if material not found.

**Tests (you must add to `tests/test_parse_and_plan.py`)**

* Prompt: “Compute PBE band structure and DOS of LiFePO4 with U(Fe)=5.3”
  → operations include `bands` and `dos`; `theory.u_settings["Fe"]==5.3`.

---

# 3) Domain Knowledge Base Prompt

**Role**: Provide policy defaults and tiny, robust helpers to generate INCAR/KPOINTS templates.

**Task**

* Add `vasp_agent/kb/policies/defaults.yaml` (if missing) with:

  * `encut_policy: "1.3*ENMAX"`
  * `kpoint_density_ppa: 1000`
  * smearing rules for metals/insulators
  * relaxation ladder: `["pre_relax","relax","static"]`
* Implement `vasp_agent/tools/policy.py`:

  * `load_defaults() -> dict`
  * `choose_smearing(is_metal_hint: bool, preset: str) -> dict`
  * `encut_from_potcars(potcar_enmax: list[float], policy: str) -> int` (use 1.3×max)
  * `default_magmom(element_list) -> dict` (very small table, e.g., Fe=5, Co=3, Ni=2; else 0)

**Task**

* Extend `vasp_agent/tools/inputs.py` to:

  * `make_incar(op, numerics, theory, metal_hint=False) -> dict`
  * `make_kpoints(structure, ppa:int) -> str` (use pymatgen automatic density by vol if available; else return a placeholder string like “Auto PPA=1000”)

**Definition of Done**

* Unit tests: `encut_from_potcars([400, 520], "1.3*ENMAX") == 676`.

---

# 4) Planner Prompt

**Role**: Convert a `TaskSpec` into a runnable `WorkflowDAG` with deterministic node order and simple checkpoints.

**Task**

* Create `vasp_agent/workflows/planner.py` with:

```python
from ..schemas import TaskSpec
from .dag import WorkflowDAG, Node

def plan_from_taskspec(ts: TaskSpec) -> WorkflowDAG: ...
```

* Nodes to include (subset based on ts.operations):

  * `pre_relax`, `relax`, `static`, `bands?`, `dos?`, `summarize`.
* Each node’s `run(ctx)` should append an entry to `ctx["plan_log"]` and prepare what the next step needs (e.g., mark `ctx["op"]="relax"`).
* `summarize` uses `reporting.write_report()` to write a minimal JSON report from `ctx["summary"]` (provided by the runner; see next prompt).

**Inputs/Outputs for nodes (contract)**

* Every node reads `ctx["workdir"]`.
* Planner must set `ctx["ops"]` (ordered list of op names) before execution.

**Definition of Done**

* `plan_from_taskspec` returns a DAG whose `linearized()` order matches the requested ops and ends with `summarize`.

---

# 5) Runner (Local Dry-Run) Prompt

**Role**: For this iteration, don’t call VASP. Simulate a run that still exercises file generation and post-processing.

**Task**

* Implement `vasp_agent/tools/runner.py` with class `LocalDryRunner` exposing:

```python
class LocalDryRunner:
    def __init__(self, base_dir="./runs"): ...
    def prepare_step(self, workdir:str, op:str, ts:TaskSpec) -> dict:
        """Write INCAR/KPOINTS/POSCAR placeholders into op-specific subfolder under workdir."""
    def execute_step(self, workdir:str, op:str) -> dict:
        """Simulate VASP by writing minimal vasprun.xml-like stub and OSZICAR text."""
    def summarize(self, workdir:str) -> dict:
        """Parse the stub and return summary dict with final_energy, band_gap, is_metal, formula."""
```

**Simulation details**

* `prepare_step` writes:

  * `INCAR` (serialize dict lines key=value),
  * `KPOINTS` (string from `make_kpoints`),
  * `POSCAR` (write a placeholder header with formula from `TaskSpec.material.value`).
* `execute_step`:

  * For `static`, write a `vasprun.xml` stub containing:

    * a fake final energy (e.g., -123.456),
    * a fake band gap (1.23 if `bands` in ops else 0.0),
    * a metal flag consistent with band gap,
    * formula string.
* `summarize` reads the stub (it can be JSON instead of XML for simplicity) and returns a dict.

**Integration**

* The CLI `run` command should:

  * parse prompt → taskspec,
  * build DAG via planner,
  * create run dir,
  * for each op node (except summarize): call `prepare_step` then `execute_step`,
  * accumulate a summary in `ctx["summary"]`,
  * call `reporting.write_report(workdir, summary)`.

**Definition of Done**

* After `run`, the directory contains subfolders per op with input files and a top-level `report.json`.

---

# 6) File & Resource Management Prompt

**Role**: Provide clean run directories, names, and minimal POTCAR mapping checks (without shipping POTCAR files).

**Task**

* Implement `vasp_agent/tools/fs.py`:

```python
from datetime import datetime
from pathlib import Path
import re, json, hashlib

def new_run_dir(base="./runs", material_hint="job") -> str:
    slug = re.sub(r'[^A-Za-z0-9]+','-', material_hint).strip('-').lower() or "job"
    ts = datetime.now().strftime("%Y%m%d-%H%M%S")
    p = Path(base)/f"{slug}-{ts}"
    p.mkdir(parents=True, exist_ok=False)
    return str(p)

def hash_text(s: str) -> str:
    return hashlib.sha1(s.encode()).hexdigest()[:10]
```

* In `vasp_agent/tools/inputs.py`, add a very small `resolve_potcar_symbols(elements: list[str]) -> list[str]` that returns plausible PAW labels (e.g., `["Li","Fe_pv","O"]` for LiFeO compounds) with a trivial rule; **do not** ship real POTCARs.

**Definition of Done**

* New runs never collide in name.
* `resolve_potcar_symbols(["Li","Fe","O"])` returns a non-empty list.

---

# 7) Minimal Tests Prompt

**Role**: Add 3–4 smoke tests to ensure the skeleton holds together.

**Task**

* Update `tests/test_parse_and_plan.py`:

```python
from vasp_agent.tools.parser import parse_prompt_to_taskspec
from vasp_agent.workflows.planner import plan_from_taskspec
from vasp_agent.tools.runner import LocalDryRunner
from vasp_agent.tools.fs import new_run_dir
import pathlib, json

def test_parse_ops_and_u():
    ts = parse_prompt_to_taskspec("Band structure and DOS of LiFePO4 with U(Fe)=5.3")
    assert any(op.type=="bands" for op in ts.operations)
    assert any(op.type=="dos" for op in ts.operations)
    assert ts.theory.u_settings.get("Fe")==5.3

def test_plan_order():
    ts = parse_prompt_to_taskspec("Compute PBE gap of LiFePO4")
    dag = plan_from_taskspec(ts)
    names = [n.name for n in dag.linearized()]
    assert names[:3]==["pre_relax","relax","static"]
    assert "summarize" in names

def test_run_smoke(tmp_path):
    ts = parse_prompt_to_taskspec("Compute PBE band gap of LiFePO4")
    run_dir = new_run_dir(base=tmp_path, material_hint="LiFePO4")
    # Fake minimal orchestration
    from vasp_agent.workflows.planner import plan_from_taskspec
    dag = plan_from_taskspec(ts)
    ctx = {"workdir": str(run_dir)}
    # Simulate node execution
    runner = LocalDryRunner(base_dir=str(tmp_path))
    for node in dag.linearized():
        if node.name == "summarize":
            ctx["summary"] = runner.summarize(ctx["workdir"])
        else:
            runner.prepare_step(ctx["workdir"], node.name, ts)
            runner.execute_step(ctx["workdir"], node.name)
    # Write report and assert
    import json, os
    report = pathlib.Path(run_dir)/"report.json"
    if not report.exists():
        from vasp_agent.tools.reporting import write_report
        write_report(str(run_dir), ctx["summary"])
    data = json.loads(report.read_text())
    assert "band_gap" in data and "final_energy" in data
```

**Definition of Done**

* `pytest -q` passes.
* Running the CLI produces a populated run directory and `report.json`.


