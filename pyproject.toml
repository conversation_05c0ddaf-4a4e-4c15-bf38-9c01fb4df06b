[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vasp-agent"
version = "0.1.0"
description = "AI-powered VASP simulation workflow management"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "VASP Agent Development Team"}
]
keywords = ["vasp", "dft", "materials", "simulation", "ai"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Chemistry",
    "Topic :: Scientific/Engineering :: Physics",
]

dependencies = [
    "pydantic>=2.0.0,<3.0.0",
    "click>=8.0.0",
    "pymatgen>=2023.1.0",
    "pyyaml>=6.0",
    "pathlib",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov",
    "black",
    "isort",
    "flake8",
]

[project.scripts]
vasp-agent = "vasp_agent.cli:main"

[project.urls]
Homepage = "https://github.com/example/vasp-agent"
Repository = "https://github.com/example/vasp-agent"
Issues = "https://github.com/example/vasp-agent/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["vasp_agent*"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
