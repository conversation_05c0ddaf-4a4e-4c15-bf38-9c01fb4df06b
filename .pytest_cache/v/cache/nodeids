["tests/test_parse_and_plan.py::test_error_handling", "tests/test_parse_and_plan.py::test_parse_basic_prompt", "tests/test_parse_and_plan.py::test_parse_fast_preset", "tests/test_parse_and_plan.py::test_parse_hse_prompt", "tests/test_parse_and_plan.py::test_parse_mp_id", "tests/test_parse_and_plan.py::test_parse_ops_and_u", "tests/test_parse_and_plan.py::test_plan_order", "tests/test_parse_and_plan.py::test_plan_with_bands_and_dos", "tests/test_parse_and_plan.py::test_policy_integration", "tests/test_parse_and_plan.py::test_run_smoke", "tests/test_parse_and_plan.py::test_runner_execute_step", "tests/test_parse_and_plan.py::test_runner_prepare_step", "tests/test_parse_and_plan.py::test_runner_summarize", "tests/test_parse_and_plan.py::test_workflow_validation"]