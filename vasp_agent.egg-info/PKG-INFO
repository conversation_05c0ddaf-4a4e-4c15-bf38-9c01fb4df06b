Metadata-Version: 2.4
Name: vasp-agent
Version: 0.1.0
Summary: AI-powered VASP simulation workflow management
Author: VASP Agent Development Team
License: MIT
Project-URL: Homepage, https://github.com/example/vasp-agent
Project-URL: Repository, https://github.com/example/vasp-agent
Project-URL: Issues, https://github.com/example/vasp-agent/issues
Keywords: vasp,dft,materials,simulation,ai
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering :: Chemistry
Classifier: Topic :: Scientific/Engineering :: Physics
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: pydantic<3.0.0,>=2.0.0
Requires-Dist: click>=8.0.0
Requires-Dist: pymatgen>=2023.1.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: pathlib
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: isort; extra == "dev"
Requires-Dist: flake8; extra == "dev"

# VASP Agent

AI-powered VASP simulation workflow management system that converts natural language requests into planned DFT workflows.

## Features

- **Natural Language Interface**: Convert plain English descriptions into VASP workflows
- **Automated Workflow Planning**: Generate optimized calculation sequences (pre-relax → relax → static → bands/DOS)
- **Policy-Driven Input Generation**: Intelligent INCAR/KPOINTS generation based on best practices
- **Dry-Run Simulation**: Test workflows without actual VASP execution
- **Comprehensive Reporting**: JSON reports with energy, band gap, and material properties

## Installation

```bash
pip install -e .
```

## Quick Start

### Plan a calculation
```bash
vasp-agent plan --material LiFePO4 --source formula
```

### Run a workflow from natural language
```bash
echo "Compute PBE band gap of LiFePO4" | vasp-agent run --stdin
```

### Run with direct prompt
```bash
vasp-agent run --prompt "Band structure and DOS of LiFePO4 with U(Fe)=5.3"
```

## Requirements

- Python 3.10+
- pydantic v2
- pymatgen
- click

## Development

```bash
pip install -e ".[dev]"
pytest
```

## License

MIT License
